# frozen_string_literal: true

require 'contexts/with_request_mock'

RSpec.describe Sentry do
  include_context "with request mock"

  let(:client) { Sentry.get_current_client }
  let(:transport) { Sentry.get_current_client.transport }

  before :all do
    perform_basic_setup
  end

  before do
    setup_sentry_test do |config|
      config.transport.transport_class = Sentry::DebugTransport
    end
  end

  after do
    teardown_sentry_test
  end

  describe ".send_event" do
    let(:event) { Sentry.get_current_client.event_from_message("test message") }

    it "sends the event and logs to a file" do
      sentry_stub_request(build_fake_response("200"))

      Sentry.send_event(event)

      expect(transport.events.count).to be(1)
    end
  end
end
